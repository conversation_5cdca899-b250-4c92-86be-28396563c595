body {
    margin: 0px !important;
    transform-origin: 0 0;
    -moz-user-select: none;
    /*火狐*/
    -webkit-user-select: none;
    /*webkit浏览器*/
    -ms-user-select: none;
    /*IE10*/
    user-select: none;
    /*选中文字时避免出现蓝色背景*/
}

body,
html,
#app {
    height: 100%;
}

@font-face {
    font-family: nanshen;
    src: url('/font/nashen.otf');
}

*,
*:before,
*:after {
    box-sizing: border-box
}


// 隐藏滚动条
::-webkit-scrollbar {
    width: 0;
    height: 0;
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis; // 显示省略符号来代表被修剪的文本。
    white-space: nowrap; // white-space 
    text-align: left;
}


canvas {
    transform: translate3d(0, 0, 0);
}

p {
    margin: 0;
    color: #fff;
}

.pointer {
    &:hover {
        cursor: pointer;
    }
}

.bolder {
    font-weight: bolder;
}

.grid {
    display: grid;
}

.relative {
    position: relative;
}

.absolute {
    position: absolute !important;
}

.flex {
    display: flex;
}

.flex-all-center {
    display: flex;
    justify-content: center;
    align-items: center;
}

.flex-direction {
    flex-direction: column;
}

.flex-wrap {
    flex-wrap: wrap;
}

.align-start {
    align-items: flex-start;
}

.align-end {
    align-items: flex-end;
}

.align-center {
    align-items: center;
}

.align-stretch {
    align-items: stretch;
}

.align-baseline {
    align-items: baseline;
}

.self-start {
    align-self: flex-start;
}

.self-end {
    align-self: flex-end;
}

.self-stretch {
    align-self: stretch;
}

.align-stretch {
    align-items: stretch;
}

.justify-start {
    justify-content: flex-start;
}

.justify-end {
    justify-content: flex-end;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.justify-around {
    justify-content: space-around;
}